import os
import glob
import numpy as np
from tqdm import tqdm
from utils import read_list, config
import SimpleITK as sitk


config = config.Config("lits")
base_dir = config.base_dir


def write_txt(data, path):
    with open(path, 'w') as f:
        for val in data:
            f.writelines(val + '\n')


def process_npy():
    for tag in ['Tr']:
        img_ids = []
        for path in tqdm(glob.glob(os.path.join(base_dir, f'images{tag}', '*.nii.gz'))):
            img_id = path.split('/')[-1].split('.')[0]
            img_ids.append(img_id)
            label_id= img_id

            image_path = os.path.join(base_dir, f'images{tag}', f'{img_id}.nii.gz')
            label_path =os.path.join(base_dir, f'labels{tag}', f'{label_id}.nii.gz')

            # Read the original ITK images to preserve spacing information
            image_itk = sitk.ReadImage(image_path)
            label_itk = sitk.ReadImage(label_path)

            # Get arrays from ITK images
            image = sitk.GetArrayFromImage(image_itk)
            label = sitk.GetArrayFromImage(label_itk)

            print('aaas')

            # Convert to appropriate data types
            image = image.astype(np.float32)
            label = label.astype(np.int8)

            # Step 1: Adjust spacing to (1.5, 1.5, 1.5)
            # Note: ITK spacing is in (x, y, z) order, but we need (z, y, x) for resampling
            target_spacing = (1.5, 1.5, 1.5)  # (z, y, x) order for array operations
            original_spacing = image_itk.GetSpacing()[::-1]  # Convert from (x,y,z) to (z,y,x)
            original_size = image_itk.GetSize()[::-1]  # Convert from (x,y,z) to (z,y,x)

            # Calculate new size based on spacing change
            zoom_factors = [orig_sp / target_sp for orig_sp, target_sp in zip(original_spacing, target_spacing)]

            # Resample image and label using SimpleITK for proper spacing handling
            resampler = sitk.ResampleImageFilter()
            resampler.SetOutputSpacing((1.5, 1.5, 1.5))  # (x, y, z) order for ITK
            resampler.SetSize([int(original_size[2] * zoom_factors[2]),
                              int(original_size[1] * zoom_factors[1]),
                              int(original_size[0] * zoom_factors[0])])  # (x, y, z) order
            resampler.SetOutputDirection(image_itk.GetDirection())
            resampler.SetOutputOrigin(image_itk.GetOrigin())
            resampler.SetTransform(sitk.Transform())
            resampler.SetDefaultPixelValue(0)

            # Resample image with linear interpolation
            resampler.SetInterpolator(sitk.sitkLinear)
            image_resampled = resampler.Execute(image_itk)
            image = sitk.GetArrayFromImage(image_resampled).astype(np.float32)

            # Resample label with nearest neighbor interpolation
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)
            label_resampled = resampler.Execute(label_itk)
            label = sitk.GetArrayFromImage(label_resampled).astype(np.int8)

            # Step 2: Scale intensity from [-175.0, 250] to [0, 1] with clipping
            image = np.clip(image, -175.0, 250.0)  # Clip values outside the range
            image = (image - (-175.0)) / (250.0 - (-175.0))  # Scale to [0, 1]

            if not os.path.exists(os.path.join(config.save_dir, 'processed')):
                os.makedirs(os.path.join(config.save_dir, 'processed'))

            # Create new ITK images with updated spacing for saving
            image_nii = sitk.GetImageFromArray(image)
            image_nii.SetSpacing((1.5, 1.5, 1.5))  # Set the target spacing
            image_nii.SetDirection(image_itk.GetDirection())
            image_nii.SetOrigin(image_itk.GetOrigin())

            label_nii = sitk.GetImageFromArray(label)
            label_nii.SetSpacing((1.5, 1.5, 1.5))  # Set the target spacing
            label_nii.SetDirection(label_itk.GetDirection())
            label_nii.SetOrigin(label_itk.GetOrigin())

            sitk.WriteImage(image_nii, os.path.join(config.save_dir, 'processed', f'{img_id}.nii.gz'))
            sitk.WriteImage(label_nii, os.path.join(config.save_dir, 'processed', f'{label_id}.nii.gz'))

            if not os.path.exists(os.path.join(config.save_dir, 'npy')):
                os.makedirs(os.path.join(config.save_dir, 'npy'))

            np.save(
                os.path.join(config.save_dir, 'npy', f'{img_id}_image.npy'),
                image
            )
            np.save(
                os.path.join(config.save_dir, 'npy', f'{img_id}_label.npy'),
                label
            )




def process_split_fully(train_ratio=0.8):


    if not os.path.exists(os.path.join(config.save_dir, 'split_txts')):
        os.makedirs(os.path.join(config.save_dir, 'split_txts'))

    for tag in ['Tr']:
        img_ids = []
        for path in tqdm(glob.glob(os.path.join(base_dir, f'images{tag}', '*.nii.gz'))):
            img_id = path.split('/')[-1].split('.')[0]
            img_ids.append(img_id)
        
        if tag == 'Tr':
            img_ids = np.random.permutation(img_ids)
            split_idx = int(len(img_ids) * train_ratio)
            train_val_ids = img_ids[:split_idx]
            test_ids = sorted(img_ids[split_idx:])

            split_idx = int(len(train_val_ids) * 5/6)
            train_ids = sorted(train_val_ids[:split_idx])
            eval_ids = sorted(train_val_ids[split_idx:])
            write_txt(
                train_ids,
                os.path.join(config.save_dir, 'split_txts/train.txt')
            )
            write_txt(
                eval_ids,
                os.path.join(config.save_dir, 'split_txts/eval.txt')
            )

            test_ids = sorted(test_ids)
            write_txt(
                test_ids,
                os.path.join(config.save_dir, 'split_txts/test.txt')
            )


def process_split_semi(split='train', labeled_ratio=0.2):
    ids_list = read_list(split, task="synapse")
    ids_list = np.random.permutation(ids_list)

    split_idx = int(len(ids_list) * labeled_ratio)
    labeled_ids = sorted(ids_list[:split_idx])
    unlabeled_ids = sorted(ids_list[split_idx:])
    
    write_txt(
        labeled_ids,
        os.path.join(config.save_dir, f'split_txts/labeled_{labeled_ratio}.txt')
    )
    write_txt(
        unlabeled_ids,
        os.path.join(config.save_dir, f'split_txts/unlabeled_{labeled_ratio}.txt')
    )


if __name__ == '__main__':
    process_npy()
    process_split_fully()
    process_split_semi(labeled_ratio=0.2)
